import { useState, useRef, useEffect, lazy, Suspense } from 'react';
import { FiPlus } from 'react-icons/fi';

interface FAQItemProps {
  question: string;
  answer: string;
  isVisible: boolean;
  index: number;
}

// WorkflowLoop-style FAQ item with DOM-oriented animations
const FAQItem = ({ question, answer, isVisible, index }: FAQItemProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const itemRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const answerRef = useRef<HTMLDivElement>(null);

  // Handle animation when item becomes visible
  useEffect(() => {
    if (isVisible && itemRef.current) {
      requestAnimationFrame(() => {
        if (itemRef.current) {
          itemRef.current.classList.add('is-visible');
        }
      });
    }
  }, [isVisible]);

  // Handle height animation for DOM-oriented approach
  useEffect(() => {
    if (!contentRef.current || !answerRef.current) return;

    if (isOpen) {
      // Get the height of the answer content
      const height = answerRef.current.offsetHeight;
      // Set explicit height for animation
      contentRef.current.style.height = `${height}px`;
    } else {
      // Set height to 0 when closing
      contentRef.current.style.height = '0px';
    }
  }, [isOpen]);

  const toggleAccordion = () => {
    setIsOpen(prevState => !prevState);
  };

  return (
    <div
      ref={itemRef}
      className="faq-item"
    >
      <div
        className="faq-question"
        onClick={toggleAccordion}
        onKeyDown={(e) => e.key === 'Enter' && toggleAccordion()}
        role="button"
        tabIndex={0}
        aria-expanded={isOpen}
      >
        <h3 className="text-lg font-medium">{question}</h3>
        <div className={`faq-icon ${isOpen ? 'open' : ''}`}>
          <FiPlus size={20} />
        </div>
      </div>
      <div
        ref={contentRef}
        className={`faq-content ${isOpen ? 'open' : ''}`}
        style={{
          '--index': index
        } as React.CSSProperties}
      >
        <div
          ref={answerRef}
          className="faq-answer"
        >
          <p>{answer}</p>
        </div>
      </div>
    </div>
  );
};

// Lazy-loaded FAQ content component
const FAQContent = ({ isVisible }: { isVisible: boolean }) => {
  const [visibleItems, setVisibleItems] = useState<boolean[]>([]);
  const itemsContainerRef = useRef<HTMLDivElement>(null);

  const faqs = [
    {
      question: 'Why should I choose automation over traditional methods?',
      answer:
        'Automation saves you time, money, and resources. Traditional content creation methods require significant manual effort and are difficult to scale. Our automation ecosystems handle repetitive tasks, streamline operations, and scale efficiently, delivering consistent content at higher volumes while maintaining quality. This results in faster ROI and the ability to serve more clients without expanding your team.',
    },
    {
      question: 'How quickly can I see results with your automation systems?',
      answer:
        'Most of our clients see immediate productivity gains within the first week of implementation. Our ecosystems are designed for rapid deployment and integration with your existing tools. The full impact typically becomes evident within 30-60 days as your team fully adapts to the new workflows and efficiencies. We provide comprehensive training and support to ensure a smooth transition and quick time-to-value.',
    },
    {
      question: 'Will automation affect the quality of our content?',
      answer:
        'Our automation systems are designed to enhance, not replace, human creativity. They handle repetitive tasks and provide AI-assisted content generation that still requires human review and approval. This approach actually improves quality by ensuring consistency and eliminating common errors and burnout, all on top of freeing your creative team to focus on high-value work instead of mundane tasks. The result is higher quality content at greater scale.',
    },
    {
      question: 'How customizable are your automation solutions?',
      answer:
        'Our Content Automation Ecosystems are 100% customized to your specific needs and workflows. We don\'t offer one-size-fits-all solutions. We begin with a thorough discovery process to understand your agency\'s unique requirements, then design and build a system that integrates with your existing tools and addresses your specific pain points. The solution evolves with your needs through our ongoing optimization process.',
    },
    {
      question: 'What kind of ROI can I expect from implementing your systems?',
      answer:
        'Our clients typically see ROI in three key areas: time savings (80-90% reduction in time spent on repetitive tasks), increased capacity (3-4x more content with the same team), and improved profit margins (30-40% increase). The specific ROI for your agency will depend on your current processes, the scope of implementation, and how effectively your team adopts the new systems—all factors we evaluate during our discovery process.',
    },
    {
      question: 'Do you offer a satisfaction guarantee?',
      answer:
        'Absolutely. We stand by our work with a 100% satisfaction guarantee. We believe that our success is measured by your success, which is why we don\'t consider a project complete until you\'re completely satisfied with the results. Our iterative approach includes regular check-ins, feedback sessions, and adjustments to ensure the automation ecosystem we build meets and exceeds your expectations. If at any point you\'re not completely satisfied, we\'ll continue refining the solution until it delivers the value you expect.',
    },
  ];

  // Set up intersection observer for animations - optimized version
  useEffect(() => {
    if (!isVisible) return;

    // Initialize visibility array
    setVisibleItems(new Array(faqs.length).fill(false));

    // Use requestIdleCallback for non-critical initialization
    const idleCallback = (typeof window !== 'undefined' && 'requestIdleCallback' in window)
      ? window.requestIdleCallback
      : ((cb: IdleRequestCallback) => setTimeout(() => cb({ didTimeout: false, timeRemaining: () => 50 }), 1));

    idleCallback(() => {
      const observerOptions = {
        root: null,
        rootMargin: '50px',
        threshold: 0.1
      };

      // Observer for individual items with optimized callback
      const itemObserver = new IntersectionObserver((entries) => {
        // Batch state updates for better performance
        const updates: { index: number, visible: boolean }[] = [];

        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            updates.push({ index, visible: true });
            itemObserver.unobserve(entry.target);
          }
        });

        if (updates.length) {
          setVisibleItems(prev => {
            const newArray = [...prev];
            updates.forEach(update => {
              newArray[update.index] = update.visible;
            });
            return newArray;
          });
        }
      }, observerOptions);

      // Observe items with a slight delay to ensure DOM is ready
      setTimeout(() => {
        if (itemsContainerRef.current) {
          const itemElements = itemsContainerRef.current.querySelectorAll('.faq-item-wrapper');
          itemElements.forEach((item, index) => {
            item.setAttribute('data-index', index.toString());
            itemObserver.observe(item);
          });
        }
      }, 100);

      return () => {
        itemObserver.disconnect();
      };
    });
  }, [faqs.length, isVisible]);

  return (
    <div className="mx-auto max-w-4xl" ref={itemsContainerRef}>
      {faqs.map((faq, index) => (
        <div
          key={`faq-${index}`}
          className="faq-item-wrapper"
          data-index={index}
        >
          <FAQItem
            question={faq.question}
            answer={faq.answer}
            isVisible={visibleItems[index]}
            index={index}
          />
        </div>
      ))}
    </div>
  );
};

const FAQ = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const [sectionVisible, setSectionVisible] = useState(false);

  // Optimized section visibility detection
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '100px',
      threshold: 0.1
    };

    // Observer for the section
    const sectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && sectionRef.current) {
          // Use requestAnimationFrame for smoother animation
          requestAnimationFrame(() => {
            if (sectionRef.current) {
              sectionRef.current.classList.add('is-visible');
              setSectionVisible(true);
              sectionObserver.unobserve(entry.target);
            }
          });
        }
      });
    }, observerOptions);

    // Observe section
    if (sectionRef.current) {
      sectionObserver.observe(sectionRef.current);
    }

    return () => {
      sectionObserver.disconnect();
    };
  }, []);

  return (
    <section
      id="faq"
      className="section-padding bg-gray-950 faq-section"
      ref={sectionRef}
    >
      <div className="container-custom">
        <div className="text-center section-header">
          <h2 className="section-title">
            Frequently Asked <span className="gradient-text">Questions</span>
          </h2>
          <p className="section-subtitle">
            Get answers to common questions about our content automation ecosystems
          </p>
        </div>

        {/* Lazy load FAQ content only when section becomes visible */}
        {sectionVisible && <FAQContent isVisible={sectionVisible} />}
      </div>
    </section>
  );
};

export default FAQ;
