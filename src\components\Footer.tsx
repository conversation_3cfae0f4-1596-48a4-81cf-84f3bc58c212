import { Link } from 'react-scroll';
import { FiArrowUp } from 'react-icons/fi';
import { useCallback } from 'react';
import Logo from './Logo';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  // Custom smooth scroll function with better control
  const scrollToTop = useCallback(() => {
    const duration = 1000; // ms
    const startPosition = window.pageYOffset;
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const elapsedTime = currentTime - startTime;

      // Easing function: easeInOutCubic
      const progress = Math.min(elapsedTime / duration, 1);
      const easeProgress = progress < 0.5
        ? 4 * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;

      window.scrollTo(0, startPosition * (1 - easeProgress));

      if (elapsedTime < duration) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  }, []);

  return (
    <footer className="bg-gray-950 py-12">
      <div className="container-custom">
        <div className="mb-8 flex flex-col items-center justify-between gap-6 border-b border-gray-800 pb-8 md:flex-row">
          <div>
            <Logo
              width={180}
              height={45}
              logoColor="white"
              systemsColor="#8B5CF6"
            />
            <p className="mt-2 text-gray-400">
              Automation solutions for digital marketing agencies
            </p>
          </div>

          <div className="flex flex-wrap justify-center md:justify-start gap-x-6 gap-y-2">
            <FooterLink to="hero" label="Home" />
            <FooterLink to="services" label="Services" />
            <FooterLink to="why-choose-us" label="Why Us" />
            <FooterLink to="process" label="Process" />
            <FooterLink to="faq" label="FAQ" />
            <FooterLink to="why-now" label="Why Now" />
            <FooterLink to="contact" label="Contact" />
          </div>

          <button
            onClick={scrollToTop}
            className="group flex items-center justify-center rounded-full border border-gray-800 bg-gray-900 p-3 transition-all duration-300 hover:bg-gray-800 hover:shadow-lg hover:shadow-purple-500/10"
            aria-label="Scroll to top"
          >
            <FiArrowUp className="h-5 w-5 text-white transition-transform duration-300 group-hover:-translate-y-1" />
          </button>
        </div>

        <div className="flex flex-col items-center justify-center text-sm text-gray-500">
          <div>
            &copy; {currentYear} Diftra. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};

interface FooterLinkProps {
  to: string;
  label: string;
}

const FooterLink = ({ to, label }: FooterLinkProps) => {
  return (
    <Link
      to={to}
      spy={true}
      smooth={true}
      offset={-80}
      duration={500}
      className="cursor-pointer font-medium text-gray-400 transition-colors hover:text-purple-400"
    >
      {label}
    </Link>
  );
};

export default Footer;
