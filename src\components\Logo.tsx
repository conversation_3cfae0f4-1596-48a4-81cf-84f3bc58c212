import React from 'react';

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
  logoColor?: string;
  systemsColor?: string;
}

const Logo: React.FC<LogoProps> = ({
  className = '',
  width = 200,
  height = 50,
  logoColor = 'white',
  systemsColor = '#8B5CF6' // purple-500
}) => {
  const symbolSize = height * 0.8;
  const fontSize = height * 0.5;

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* Logo Symbol */}
      <svg
        width={symbolSize}
        height={symbolSize}
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="flex-shrink-0"
      >
        <path
          d="M10 15C10 15 10 15 10 15C10 15 10 15 10 15L75 5C75 5 75 5 75 5C75 5 75 5 75 5C82.5 5 90 12.5 90 20C90 27.5 82.5 35 75 35L35 45L55 65L75 55C82.5 55 90 62.5 90 70C90 77.5 82.5 85 75 85L10 95C10 95 10 95 10 95C10 95 10 95 10 95C2.5 95 -5 87.5 -5 80C-5 72.5 2.5 65 10 65L50 55L30 35L10 45C2.5 45 -5 37.5 -5 30C-5 22.5 2.5 15 10 15Z"
          fill={logoColor}
        />
        <path
          d="M35 25L60 50L45 60L35 25Z"
          fill={logoColor}
        />
      </svg>

      {/* Text using HTML elements for better rendering */}
      <div className="flex items-baseline">
        <span
          className="font-bold"
          style={{
            color: logoColor,
            fontSize: `${fontSize}px`,
            lineHeight: 1,
            fontFamily: 'system-ui, -apple-system, sans-serif'
          }}
        >
          Diftra
        </span>
        <span
          className="font-bold ml-1"
          style={{
            color: systemsColor,
            fontSize: `${fontSize}px`,
            lineHeight: 1,
            fontFamily: 'system-ui, -apple-system, sans-serif'
          }}
        >
          Systems
        </span>
      </div>
    </div>
  );
};

export default Logo;
