import { useEffect, useRef } from 'react';
import { FaBrain, FaRobot, FaExpand } from 'react-icons/fa';
import { FaPuzzlePiece } from 'react-icons/fa6';

const Services = () => {
  const services = [
    {
      icon: <FaBrain className="h-10 w-10" />,
      title: 'Content Strategy Automation',
      description:
        'We build systems that automate your content planning, scheduling, and distribution strategy, ensuring consistent and effective content delivery across all channels.',
    },
    {
      icon: <FaRobot className="h-10 w-10" />,
      title: 'AI Content Generation',
      description:
        'Leverage cutting-edge AI to create, refine, and scale your content production while maintaining your brand voice and quality standards.',
    },
    {
      icon: <FaExpand className="h-10 w-10" />,
      title: 'Scalable System Architecture',
      description:
        'Our automation ecosystems are built to grow with your agency, easily accommodating new clients, channels, and content types without requiring a complete rebuild.',
    },
    {
      icon: <FaPuzzlePiece className="h-10 w-10" />,
      title: 'Workflow Integration',
      description:
        'Seamlessly integrate your tools and platforms into a cohesive ecosystem that eliminates manual tasks and streamlines your entire content operation.',
    },
  ];

  // Refs for animation elements
  const titleRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<(HTMLDivElement | null)[]>([]);
  const solutionsRef = useRef<HTMLDivElement>(null);

  // Handle animations with plain JS
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const viewportHeight = window.innerHeight;

      // Animate title
      if (titleRef.current) {
        const titleTop = titleRef.current.getBoundingClientRect().top + scrollY;
        if (scrollY + viewportHeight * 0.8 > titleTop) {
          titleRef.current.style.opacity = '1';
          titleRef.current.style.transform = 'translateY(0)';
        }
      }

      // Animate service cards
      cardsRef.current.forEach((card, index) => {
        if (card) {
          const cardTop = card.getBoundingClientRect().top + scrollY;
          if (scrollY + viewportHeight * 0.8 > cardTop) {
            setTimeout(() => {
              if (card) {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
              }
            }, index * 80);
          }
        }
      });

      // Animate solutions section
      if (solutionsRef.current) {
        const solutionsTop = solutionsRef.current.getBoundingClientRect().top + scrollY;
        if (scrollY + viewportHeight * 0.8 > solutionsTop) {
          setTimeout(() => {
            if (solutionsRef.current) {
              solutionsRef.current.style.opacity = '1';
              solutionsRef.current.style.transform = 'translateY(0)';
            }
          }, 300);
        }
      }
    };

    // Run once on mount
    handleScroll();

    // Add scroll listener
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <section id="services" className="section-padding bg-gray-900">
      <div className="container-custom">
        <div
          ref={titleRef}
          className="services-animate"
          style={{
            opacity: 0,
            transform: 'translateY(20px)',
            transition: 'opacity 0.5s ease, transform 0.5s ease'
          }}
        >
          <h2 className="section-title">
            Our <span className="gradient-text">Automation</span> Services
          </h2>
          <p className="section-subtitle">
            We build comprehensive content automation ecosystems that transform how your agency operates
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {services.map((service, index) => (
            <div
              key={index}
              ref={el => cardsRef.current[index] = el}
              className="services-card flex flex-col rounded-lg border border-gray-800 bg-gray-800/50 p-6"
              style={{
                opacity: 0,
                transform: 'translateY(20px)',
                transition: 'opacity 0.5s ease, transform 0.5s ease'
              }}
            >
              <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-700/20 text-purple-400">
                {service.icon}
              </div>
              <h3 className="mb-3 text-xl font-bold">{service.title}</h3>
              <p className="text-gray-400">{service.description}</p>
            </div>
          ))}
        </div>

        {/* Additional section highlighting comprehensive solutions */}
        <div
          ref={solutionsRef}
          className="services-animate mt-16 rounded-lg border border-gray-800 bg-gray-800/30 p-8"
          style={{
            opacity: 0,
            transform: 'translateY(20px)',
            transition: 'opacity 0.5s ease, transform 0.5s ease'
          }}
        >
          <h3 className="mb-4 text-center text-2xl font-bold">
            Complete <span className="gradient-text">End-to-End</span> Solutions
          </h3>
          <p className="mb-6 text-center text-gray-400">
            Unlike tools that solve only one part of the problem, our automation ecosystems address your
            entire content workflow from top to bottom.
          </p>

          <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="rounded-lg border border-gray-800 bg-gray-800/50 p-4 text-center">
              <div className="text-xl font-bold text-purple-400">Ideation & Planning</div>
              <div className="mt-2 text-sm text-gray-400">Automated content calendars and idea generation</div>
            </div>
            <div className="rounded-lg border border-gray-800 bg-gray-800/50 p-4 text-center">
              <div className="text-xl font-bold text-purple-400">Creation & Delivery</div>
              <div className="mt-2 text-sm text-gray-400">AI-assisted content creation and multichannel distribution</div>
            </div>
            <div className="rounded-lg border border-gray-800 bg-gray-800/50 p-4 text-center">
              <div className="text-xl font-bold text-purple-400">Maintenance & Optimization</div>
              <div className="mt-2 text-sm text-gray-400">Consistent performance and continuous improvement</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
