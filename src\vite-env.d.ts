/// <reference types="vite/client" />

// Add requestIdleCallback types
interface Window {
  requestIdleCallback: (
    callback: (deadline: {
      didTimeout: boolean;
      timeRemaining: () => number;
    }) => void,
    opts?: { timeout: number }
  ) => number;
  cancelIdleCallback: (handle: number) => void;
}

interface IdleRequestCallback {
  (deadline: {
    didTimeout: boolean;
    timeRemaining: () => number;
  }): void;
}
